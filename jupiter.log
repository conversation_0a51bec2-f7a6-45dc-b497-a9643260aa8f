[2m2025-08-06T00:16:34.014764Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Version: 6.0.53 12676cc92a6f5f978b89406c08015e4cfd177db9
[2m2025-08-06T00:16:34.015940Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Loading Jupiter router...
[2m2025-08-06T00:16:34.016040Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Using market cache kind: Remote { url: "https://cache.jup.ag/markets?v=4" }    
[2m2025-08-06T00:16:34.016094Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Running router with 2 threads in the CPU bound work thread pool
[2m2025-08-06T00:16:36.137861Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m Excluded 188182 AMMs based on dexes id selection: Include({9H6tua7jkLhdm3w8BvgpTn5LZNU7g4ZynDmCiNN3q6Rp, 2wT8Yq49kHgDzXuPxZSaeLaH1qbmGXtEyPy64bL7aD3c, goonERTdGsjnkZqWuVjs73BZ3Pb9qoCUdBUL17BnS5j, SoLFiHG9TfgtdUXUjWAxi3LtvYuFyDLVhBWxdMZxyCe, HpNfyc2Saw7RKkQd8nEL4khUcuPhQ7WwY1B2qjx8jxFq})    
[2m2025-08-06T00:16:36.703173Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m markets len(): 97    
[2m2025-08-06T00:16:49.919458Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m Found 0 not supported markets for owners: {}    
[2m2025-08-06T00:16:59.499186Z[0m [33m WARN[0m [2mjupiter_core::amms::loader[0m[2m:[0m Stakedex AMMs where not found in the cache, 0 out of 955650    
[2m2025-08-06T00:16:59.500339Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m New dexes are disabled, [] are not loaded    
[2m2025-08-06T00:16:59.500382Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m updating amms with account_keys.len(): 0    
[2m2025-08-06T00:16:59.500510Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m Excluded 80 AMMs based on filter markets with mints: {So11111111111111111111111111111111111111112, 9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump, EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v}    
[2m2025-08-06T00:16:59.500529Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m Loaded 17 amms from market cache    
[2m2025-08-06T00:16:59.500568Z[0m [32m INFO[0m [2mjupiter_core::amms::loader[0m[2m:[0m AMMS per label loaded: {"SolFi": 3, "PancakeSwap": 3, "GoonFi": 2, "Lifinity V2": 5, "HumidiFi": 4}    
[2m2025-08-06T00:16:59.501015Z[0m [32m INFO[0m [2mjupiter_core::address_lookup_table_cache[0m[2m:[0m Loading AddressLookupTableCache...    
[2m2025-08-06T00:16:59.501033Z[0m [32m INFO[0m [2mjupiter_core::address_lookup_table_cache[0m[2m:[0m Fetching 15 address lookup tables    
[2m2025-08-06T00:16:59.660039Z[0m [32m INFO[0m [2mjupiter_core::address_lookup_table_cache[0m[2m:[0m Update 15 ALTs into the AddressLookupTableCache in 158.985174ms    
[2m2025-08-06T00:16:59.660113Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m 17 amms for 17 lookup tables    
[2m2025-08-06T00:16:59.711808Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m Fetched 33 referral token accounts    
[2m2025-08-06T00:16:59.711850Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m Fetching 3 mints...    
[2m2025-08-06T00:17:00.227854Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m Fetched 3 mints into the mint repository    
[2m2025-08-06T00:17:00.270666Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m Perform initial update #1    
[2m2025-08-06T00:17:00.270744Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m create key_set: 35.84µs    
[2m2025-08-06T00:17:00.555991Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m getMA + insert 285.22ms    
[2m2025-08-06T00:17:00.556363Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m update: 315.04µs    
[2m2025-08-06T00:17:00.556373Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m Perform initial update #2    
[2m2025-08-06T00:17:00.556387Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m create key_set: 8.03µs    
[2m2025-08-06T00:17:00.822435Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m getMA + insert 266.02ms    
[2m2025-08-06T00:17:00.822656Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m update: 152.13µs    
[2m2025-08-06T00:17:00.822706Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m Perform initial update #3    
[2m2025-08-06T00:17:00.822783Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m create key_set: 70.10µs    
[2m2025-08-06T00:17:00.822804Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m getMA + insert 5.13µs    
[2m2025-08-06T00:17:00.822810Z[0m [32m INFO[0m [2mjupiter_core::router[0m[2m:[0m update: 1.06µs    
[2m2025-08-06T00:17:00.823199Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 380.814µs    
[2m2025-08-06T00:17:00.823227Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Loaded Jupiter router in 26.80729198s
[2m2025-08-06T00:17:00.823233Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Starting Jupiter router background thread...
[2m2025-08-06T00:17:00.824598Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:00.824876Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m geyser_subscribe_accounts_loop start, initial filter with 77    
[2m2025-08-06T00:17:00.824962Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Starting geyser client with accounts chunk size: 16    
[2m2025-08-06T00:17:00.825022Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Connect to subscribe to the geyser server    
[2m2025-08-06T00:17:00.825092Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 498.049µs    
[2m2025-08-06T00:17:00.825408Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 175.514µs    
[2m2025-08-06T00:17:00.833495Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Connect to subscribe to the geyser server    
[2m2025-08-06T00:17:00.840228Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Connect to subscribe to the geyser server    
[2m2025-08-06T00:17:00.845353Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Connect to subscribe to the geyser server    
[2m2025-08-06T00:17:00.850400Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Connect to subscribe to the geyser server    
[2m2025-08-06T00:17:00.857992Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Connect to subscribe to the geyser server    
[2m2025-08-06T00:17:00.863684Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m subscribed to new accounts    
[2m2025-08-06T00:17:00.863749Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m stream opened    
[2m2025-08-06T00:17:00.863785Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m subscribed to new accounts    
[2m2025-08-06T00:17:00.863792Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m stream opened    
[2m2025-08-06T00:17:00.863804Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m subscribed to new accounts    
[2m2025-08-06T00:17:00.863810Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m stream opened    
[2m2025-08-06T00:17:00.863821Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m subscribed to new accounts    
[2m2025-08-06T00:17:00.863827Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m stream opened    
[2m2025-08-06T00:17:00.863836Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m subscribed to new accounts    
[2m2025-08-06T00:17:00.863841Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m stream opened    
[2m2025-08-06T00:17:00.866177Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m subscribed to new accounts    
[2m2025-08-06T00:17:00.866209Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m stream opened    
[2m2025-08-06T00:17:00.877634Z[0m [32m INFO[0m [2mjupiter_core::slippage::dynamic_slippage_range_picker[0m[2m:[0m Updated dynamic slippage range picker    
[2m2025-08-06T00:17:00.877744Z[0m [32m INFO[0m [2mjupiter_core::slippage::dynamic_slippage_range_picker[0m[2m:[0m Updated intermediate tokens    
[2m2025-08-06T00:17:00.906862Z[0m [32m INFO[0m [2mjupiter_core::slippage::dynamic_slippage_range_picker[0m[2m:[0m Updated dynamic slippage range picker    
[2m2025-08-06T00:17:00.906927Z[0m [32m INFO[0m [2mjupiter_core::slippage::dynamic_slippage_range_picker[0m[2m:[0m Updated intermediate tokens    
[2m2025-08-06T00:17:01.074598Z[0m [32m INFO[0m [2mjupiter_core::update_from_geyser[0m[2m:[0m Maintenance poll, minimum context slot ********* in 249.65404ms for 77 keys    
[2m2025-08-06T00:17:05.777403Z[0m [31mERROR[0m [2mjupiter_core::swap_transaction::blockhash_provider[0m[2m:[0m Found only 0 blockhashes, RPC failed or block did not confirm: [RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; , RPC response error -32011: Transaction history is not available from this node; 

Caused by:
    RPC response error -32011: Transaction history is not available from this node; ]    
[2m2025-08-06T00:17:05.777559Z[0m [32m INFO[0m [2mjupiter_core::swap_transaction::blockhash_provider[0m[2m:[0m Seeded slot_to_blockhash_and_block_height with 0 blockhashes    
[2m2025-08-06T00:17:05.777614Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Started Jupiter router background thread.
[2m2025-08-06T00:17:05.779250Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Running with a total of 8 logical cores, using 2 tokio worker threads for the api
[2m2025-08-06T00:17:05.779762Z[0m [32m INFO[0m [2mjupiter_swap_api[0m[2m:[0m Starting Jupiter swap api on 127.0.0.1:8080
[2m2025-08-06T00:17:05.825013Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 220.873µs    
[2m2025-08-06T00:17:05.826137Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:10.825849Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 309.847µs    
[2m2025-08-06T00:17:10.826994Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:10.837455Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:10.854411Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:10.855104Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:10.859088Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:10.863262Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:10.867926Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:15.825454Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 204.387µs    
[2m2025-08-06T00:17:15.828694Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:20.825450Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 377.549µs    
[2m2025-08-06T00:17:20.829763Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:20.838168Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:20.855035Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:20.858170Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:20.861177Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:20.865183Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:20.868628Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:25.825373Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 249.056µs    
[2m2025-08-06T00:17:25.831614Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:30.825463Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 319.401µs    
[2m2025-08-06T00:17:30.832738Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:30.839453Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:30.855479Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:30.855650Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:30.862186Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:30.866514Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:30.869888Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:35.825195Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 268.355µs    
[2m2025-08-06T00:17:35.834378Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.11ms    
[2m2025-08-06T00:17:40.825396Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 303.717µs    
[2m2025-08-06T00:17:40.835643Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:40.843747Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:40.857099Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:40.857225Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:40.863276Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:40.867560Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:40.871051Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:45.825355Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 351.74µs    
[2m2025-08-06T00:17:45.837618Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.08ms    
[2m2025-08-06T00:17:50.825816Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 355.225µs    
[2m2025-08-06T00:17:50.838541Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:17:50.842175Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:50.858887Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:50.859125Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:50.865180Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:50.868248Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:50.871842Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:17:55.826028Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 323.337µs    
[2m2025-08-06T00:17:55.838740Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:18:00.825318Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 316.256µs    
[2m2025-08-06T00:18:00.840036Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:18:00.843385Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:00.860032Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:00.860225Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:00.865254Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:00.869519Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:00.872884Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:01.328445Z[0m [32m INFO[0m [2mjupiter_core::update_from_geyser[0m[2m:[0m Maintenance poll, minimum context slot 358139125 in 252.714593ms for 77 keys    
[2m2025-08-06T00:18:05.826037Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 374.113µs    
[2m2025-08-06T00:18:05.841132Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:18:10.825615Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 314.474µs    
[2m2025-08-06T00:18:10.842771Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
[2m2025-08-06T00:18:10.846907Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:10.860023Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:10.860160Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:10.866530Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:10.870449Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:10.873622Z[0m [32m INFO[0m [2mjupiter_core::geyser_client[0m[2m:[0m Received geyser ping: SubscribeUpdatePing    
[2m2025-08-06T00:18:15.825100Z[0m [32m INFO[0m [2mjupiter_core::price_tracker[0m[2m:[0m Price tracker updated 3 prices in 226.181µs    
[2m2025-08-06T00:18:15.844439Z[0m [32m INFO[0m [2mjupiter_core::metrics::thread_pool_metrics[0m[2m:[0m Thread pool metrics - waiting: 0, active: 0, max wait: 0.00ms    
