#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
readonly COLOR_RED='\033[0;31m'
readonly COLOR_GREEN='\033[0;32m'
readonly COLOR_YELLOW='\033[1;33m'
readonly COLOR_BLUE='\033[0;34m'
readonly COLOR_RESET='\033[0m'

# 配置
RPC_URL=${RPC_URL:-"http://************:8899"}
JUPITER_PORT=${JUPITER_PORT:-8080}
JUPITER_HOST=${JUPITER_HOST:-"127.0.0.1"}
JUPITER_PID_FILE="$SCRIPT_DIR/jupiter.pid"
JUPITER_LOG_FILE="$SCRIPT_DIR/jupiter.log"
YELLOWSTONE_URL=${YELLOWSTONE_URL:-"http://***************:10000"}
YELLOWSTONE_TOKEN=${YELLOWSTONE_TOKEN:-""}

# 排除的DEX市场ID (meme市场和其他低效率市场)
# 这些是从您提供的脚本中提取的常见排除项
EXCLUDED_DEX_IDS="675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8,pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA,MoonCVVNZFSYkqNXP6bxHLPL6QQJiMagDL3qcqUQTrG,5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h"

# 日志函数
log_info() {
    echo -e "${COLOR_GREEN}[INFO]${COLOR_RESET} $1"
}

log_error() {
    echo -e "${COLOR_RED}[ERROR]${COLOR_RESET} $1"
}

log_warning() {
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_RESET} $1"
}

# 确保二进制文件有执行权限
ensure_executable() {
    if [ ! -f "$SCRIPT_DIR/jupiter-swap-api" ]; then
        log_error "未找到jupiter-swap-api二进制文件！"
        exit 1
    fi
    
    chmod +x "$SCRIPT_DIR/jupiter-swap-api"
    log_info "已设置jupiter-swap-api执行权限"
}

# 彻底停止Jupiter进程
stop_jupiter() {
    log_info "正在停止Jupiter API服务..."
    
    # 首先尝试从PID文件获取PID
    if [ -f "$JUPITER_PID_FILE" ]; then
        local pid=$(cat "$JUPITER_PID_FILE")
        if [ -n "$pid" ]; then
            log_info "从PID文件找到进程: $pid"
            # 尝试使用SIGTERM优雅终止
            kill -15 "$pid" 2>/dev/null
            
            # 等待进程终止
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                log_info "等待进程终止... ($count/10)"
                sleep 1
                ((count++))
            done
            
            # 如果进程仍然存在，使用SIGKILL强制终止
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "进程未响应SIGTERM，使用SIGKILL强制终止"
                kill -9 "$pid" 2>/dev/null
                sleep 2
            fi
        fi
        rm -f "$JUPITER_PID_FILE"
    fi
    
    # 查找并终止所有jupiter-swap-api进程，以防有遗漏
    local remaining_pids=$(pgrep -f "jupiter-swap-api")
    if [ -n "$remaining_pids" ]; then
        log_warning "发现其他jupiter-swap-api进程，正在终止: $remaining_pids"
        for pid in $remaining_pids; do
            kill -9 "$pid" 2>/dev/null
        done
        sleep 2
    fi
    
    # 最后确认没有残留进程
    if pgrep -f "jupiter-swap-api" >/dev/null; then
        log_error "无法完全终止所有jupiter-swap-api进程"
        return 1
    else
        log_info "Jupiter API服务已成功停止"
        return 0
    fi
}

# 启动Jupiter服务
start_jupiter() {
    ensure_executable
    
    # 先停止可能存在的实例
    stop_jupiter
    
    log_info "正在启动Jupiter API服务..."
    
    # 构建命令
    local cmd="$SCRIPT_DIR/jupiter-swap-api --rpc-url $RPC_URL --port $JUPITER_PORT --host $JUPITER_HOST"
    
    # 添加Yellowstone GRPC配置（如果有）
    if [ -n "$YELLOWSTONE_URL" ]; then
        cmd="$cmd --yellowstone-grpc-endpoint $YELLOWSTONE_URL"
        if [ -n "$YELLOWSTONE_TOKEN" ]; then
            cmd="$cmd --yellowstone-grpc-x-token $YELLOWSTONE_TOKEN"
        fi
    fi
    
    # 添加其他常用参数
    cmd="$cmd --allow-circular-arbitrage --market-mode remote"
    
    # 添加过滤市场（默认为WSOL和USDC）
    cmd="$cmd --filter-markets-with-mints So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
    
    # 添加排除的DEX市场
    cmd="$cmd --exclude-dex-program-ids $EXCLUDED_DEX_IDS"
    
    # 设置文件描述符限制
    ulimit -n 100000
    
    # 启动服务
    log_info "执行命令: $cmd"
    RUST_LOG=info nohup $cmd > "$JUPITER_LOG_FILE" 2>&1 &
    
    # 保存PID
    echo $! > "$JUPITER_PID_FILE"
    
    # 等待服务启动
    log_info "等待Jupiter API服务启动..."
    local count=0
    local max_wait=30
    local started=false
    
    while [ $count -lt $max_wait ]; do
        if grep -q "Listening on" "$JUPITER_LOG_FILE" || grep -q "0.0.0.0:$JUPITER_PORT" "$JUPITER_LOG_FILE"; then
            started=true
            break
        fi
        
        # 检查进程是否还在运行
        if [ ! -f "$JUPITER_PID_FILE" ] || ! kill -0 $(cat "$JUPITER_PID_FILE") 2>/dev/null; then
            log_error "Jupiter API进程已终止，请检查日志文件了解详情"
            return 1
        fi
        
        sleep 1
        ((count++))
        echo -n "."
    done
    echo ""
    
    if [ "$started" = true ]; then
        log_info "Jupiter API服务已成功启动，PID: $(cat "$JUPITER_PID_FILE")"
        log_info "API地址: http://$JUPITER_HOST:$JUPITER_PORT"
        log_info "远程访问地址: http://$(curl -s ifconfig.me):$JUPITER_PORT (如果防火墙允许)"
        return 0
    else
        log_error "启动超时，请检查日志文件了解详情"
        return 1
    fi
}

# 检查Jupiter服务状态
check_status() {
    if [ -f "$JUPITER_PID_FILE" ]; then
        local pid=$(cat "$JUPITER_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "Jupiter API服务正在运行 (PID: $pid)"
            log_info "API地址: http://$JUPITER_HOST:$JUPITER_PORT"
            log_info "远程访问地址: http://$(curl -s ifconfig.me):$JUPITER_PORT (如果防火墙允许)"
            return 0
        else
            log_warning "Jupiter API服务未运行 (PID文件存在但进程已终止)"
            rm -f "$JUPITER_PID_FILE"
        fi
    else
        log_info "Jupiter API服务未运行"
    fi
    return 1
}

# 查看日志
view_logs() {
    if [ -f "$JUPITER_LOG_FILE" ]; then
        tail -f "$JUPITER_LOG_FILE"
    else
        log_error "日志文件不存在"
        return 1
    fi
}

# 测试API是否正常工作
test_api() {
    log_info "测试Jupiter API是否正常工作..."
    
    # 构建测试URL
    local test_url="http://127.0.0.1:$JUPITER_PORT/quote?inputMint=So11111111111111111111111111111111111111112&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=10000000"
    
    # 发送请求
    local response=$(curl -s -m 10 --retry 3 "$test_url")
    
    # 检查响应
    if [ -z "$response" ]; then
        log_error "API未返回响应"
        return 1
    fi
    
    # 检查是否为有效JSON
    if ! echo "$response" | jq -e . >/dev/null 2>&1; then
        log_error "API返回的不是有效JSON"
        echo "$response"
        return 1
    fi
    
    # 检查是否包含预期字段
    if echo "$response" | jq -e '.outAmount' >/dev/null 2>&1; then
        log_info "API测试成功，返回了有效的报价数据"
        echo "$response" | jq '.'
        return 0
    else
        log_error "API返回数据缺少预期字段"
        echo "$response" | jq '.'
        return 1
    fi
}

# 添加screen会话管理功能
start_screen() {
    if ! command -v screen &> /dev/null; then
        log_error "未安装screen命令。请先安装: sudo apt-get install screen"
        return 1
    fi
    
    screen -dmS jupiter "$0" start
    log_info "已在screen会话'jupiter'中启动服务"
    log_info "使用 'screen -r jupiter' 查看会话"
}

# 命令行参数处理
case "$1" in
    start)
        start_jupiter
        ;;
    stop)
        stop_jupiter
        ;;
    restart)
        stop_jupiter
        sleep 2
        start_jupiter
        ;;
    status)
        check_status
        ;;
    logs)
        view_logs
        ;;
    test)
        test_api
        ;;
    screen)
        start_screen
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|test|screen}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动Jupiter API服务"
        echo "  stop    - 停止Jupiter API服务"
        echo "  restart - 重启Jupiter API服务"
        echo "  status  - 查看Jupiter API服务状态"
        echo "  logs    - 查看Jupiter API服务日志"
        echo "  test    - 测试Jupiter API是否正常工作"
        echo "  screen  - 在screen会话中启动服务(推荐)"
        echo ""
        echo "环境变量:"
        echo "  RPC_URL         - Solana RPC节点URL (默认: https://mainnet-ams.chainbuff.com)"
        echo "  JUPITER_PORT    - Jupiter API端口 (默认: 8080)"
        echo "  JUPITER_HOST    - Jupiter API主机 (默认: 0.0.0.0)"
        echo "  YELLOWSTONE_URL - Yellowstone GRPC端点 (可选)"
        echo "  YELLOWSTONE_TOKEN - Yellowstone GRPC令牌 (可选)"
        exit 1
        ;;
esac

exit $? 
